<template>
  <div class="message-templates-container">
    <el-card shadow="never" class="message-templates-card">
      <template #header>
        <div class="card-header">
          <h2>自定义消息模板</h2>
          <div class="header-right">
            <el-button type="primary" @click="handleCreateTemplate">
              <el-icon><Plus /></el-icon>新建模板
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索过滤 -->
      <div class="filter-container">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="模板名称">
            <el-input v-model="searchForm.search" placeholder="搜索模板名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="通知类型">
            <el-select v-model="searchForm.notificationType" placeholder="选择通知类型" clearable>
              <el-option label="订单消息" value="exchange"></el-option>
              <el-option label="系统通知" value="system"></el-option>
              <el-option label="用户消息" value="user"></el-option>
              <el-option label="反馈回复" value="feedback"></el-option>
              <el-option label="日报周报" value="report"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.isActive" placeholder="选择状态" clearable>
              <el-option label="启用" :value="true"></el-option>
              <el-option label="禁用" :value="false"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 模板列表 -->
      <el-table :data="templateList" style="width: 100%" v-loading="loading" border>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="templateName" label="模板名称" min-width="150"></el-table-column>
        <el-table-column prop="templateCode" label="模板代码" min-width="120"></el-table-column>
        <el-table-column prop="notificationType" label="通知类型" width="120">
          <template #default="scope">
            <el-tag 
              :type="getNotificationTypeTag(scope.row.notificationType)" 
              effect="plain"
            >
              {{ getNotificationTypeName(scope.row.notificationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="templateType" label="模板类型" width="100">
          <template #default="scope">
            <el-tag size="small" :type="getTemplateTypeTag(scope.row.templateType)">
              {{ getTemplateTypeName(scope.row.templateType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isDefault" label="默认模板" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.isDefault" type="success" size="small">是</el-tag>
            <el-tag v-else type="info" size="small">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="usageCount" label="使用次数" width="100"></el-table-column>
        <el-table-column prop="isActive" label="状态" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.isActive"
              active-text="启用"
              inactive-text="禁用"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handlePreview(scope.row)">预览</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-card>

    <!-- 模板表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑模板' : '新建模板'"
      width="60%"
      :before-close="handleDialogClose"
      destroy-on-close
    >
      <el-form :model="templateForm" :rules="templateRules" ref="templateFormRef" label-width="100px">
        <el-form-item label="模板名称" prop="templateName">
          <el-input v-model="templateForm.templateName" placeholder="请输入模板名称"></el-input>
        </el-form-item>
        <el-form-item label="模板代码" prop="templateCode">
          <el-input v-model="templateForm.templateCode" placeholder="请输入模板代码（字母、数字、下划线）"></el-input>
        </el-form-item>
        <el-form-item label="通知类型" prop="notificationType">
          <el-select v-model="templateForm.notificationType" placeholder="请选择通知类型">
            <el-option label="订单消息" value="exchange"></el-option>
            <el-option label="系统通知" value="system"></el-option>
            <el-option label="用户消息" value="user"></el-option>
            <el-option label="反馈回复" value="feedback"></el-option>
            <el-option label="日报周报" value="report"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板类型" prop="templateType">
          <el-radio-group v-model="templateForm.templateType">
            <el-radio label="text">纯文本</el-radio>
            <el-radio label="card">卡片消息</el-radio>
            <el-radio label="rich">富文本</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="模板内容" prop="templateContent">
          <el-input
            v-model="templateForm.templateContent"
            type="textarea"
            :rows="10"
            placeholder="请输入JSON格式的模板内容"
          ></el-input>
          <div class="variable-hint">
            <p>支持变量：使用 <span v-pre>{{ 变量名 }}</span> 格式，例如 <span v-pre>{{ orderNumber }}</span></p>
            <p>条件语法：<span v-pre>{{ #if 条件 }}</span> 内容 <span v-pre>{{ /if }}</span></p>
          </div>
        </el-form-item>
        <el-form-item label="可用变量" prop="variables">
          <el-tag
            v-for="(variable, index) in variablesList"
            :key="index"
            class="variable-tag"
            closable
            @close="handleRemoveVariable(index)"
          >
            {{ variable.name }}
          </el-tag>
          <el-input
            v-if="variableInputVisible"
            ref="variableInputRef"
            v-model="variableInput"
            class="variable-input"
            size="small"
            @keyup.enter="handleAddVariable"
            @blur="handleAddVariable"
          ></el-input>
          <el-button v-else size="small" @click="showVariableInput">
            + 添加变量
          </el-button>
        </el-form-item>
        <el-form-item label="模板描述" prop="description">
          <el-input v-model="templateForm.description" placeholder="请输入模板描述"></el-input>
        </el-form-item>
        <el-form-item label="默认模板">
          <el-switch v-model="templateForm.isDefault"></el-switch>
          <span class="hint">设为默认后，同类型的其他模板将不再是默认</span>
        </el-form-item>
        <el-form-item label="启用状态">
          <el-switch v-model="templateForm.isActive"></el-switch>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">确定</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="success" @click="testRender">测试渲染</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="模板预览"
      width="50%"
      destroy-on-close
    >
      <div class="preview-container">
        <div class="preview-header">
          <h3>{{ currentTemplate?.templateName }}</h3>
          <p class="preview-type">{{ getNotificationTypeName(currentTemplate?.notificationType) }} / {{ getTemplateTypeName(currentTemplate?.templateType) }}</p>
        </div>
        <div class="preview-content">
          <pre v-if="renderedTemplate">{{ JSON.stringify(renderedTemplate, null, 2) }}</pre>
          <div v-else class="preview-empty">
            点击"测试渲染"查看效果
          </div>
        </div>
        <div class="preview-variables">
          <h4>测试变量</h4>
          <el-form :inline="true">
            <el-form-item v-for="(variable, index) in variablesList" :key="index" :label="variable.name">
              <el-input v-model="testVariables[variable.name]" placeholder="测试值"></el-input>
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="handleRenderPreview">测试渲染</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import axios from 'axios';

// 数据定义
const loading = ref(false);
const templateList = ref([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);
const dialogVisible = ref(false);
const previewVisible = ref(false);
const isEdit = ref(false);
const templateFormRef = ref(null);
const currentTemplate = ref(null);
const renderedTemplate = ref(null);
const variableInputRef = ref(null);
const variableInputVisible = ref(false);
const variableInput = ref('');
const variablesList = ref([]);
const testVariables = ref({});

// 搜索表单
const searchForm = reactive({
  search: '',
  notificationType: '',
  isActive: undefined,
});

// 模板表单
const templateForm = reactive({
  id: null,
  templateName: '',
  templateCode: '',
  notificationType: '',
  templateType: 'card',
  templateContent: '',
  variables: '',
  description: '',
  isDefault: false,
  isActive: true,
});

// 表单验证规则
const templateRules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  templateCode: [
    { required: true, message: '请输入模板代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  notificationType: [
    { required: true, message: '请选择通知类型', trigger: 'change' }
  ],
  templateType: [
    { required: true, message: '请选择模板类型', trigger: 'change' }
  ],
  templateContent: [
    { required: true, message: '请输入模板内容', trigger: 'blur' }
  ]
};

// 初始化
onMounted(() => {
  fetchTemplates();
});

// 获取模板列表
const fetchTemplates = async () => {
  loading.value = true;
  try {
    const params = {
      page: page.value,
      pageSize: pageSize.value,
      ...searchForm
    };
    
    const response = await axios.get('/api/message-templates', { params });
    
    if (response.data.code === 0) {
      templateList.value = response.data.data.data;
      total.value = response.data.data.total;
    } else {
      ElMessage.error(response.data.message || '获取模板列表失败');
    }
  } catch (error) {
    console.error('获取模板列表失败:', error);
    ElMessage.error('获取模板列表失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  page.value = 1;
  fetchTemplates();
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  searchForm.isActive = undefined;
  handleSearch();
};

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchTemplates();
};

const handleCurrentChange = (val) => {
  page.value = val;
  fetchTemplates();
};

// 新建模板
const handleCreateTemplate = () => {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 编辑模板
const handleEdit = (row) => {
  isEdit.value = true;
  Object.keys(templateForm).forEach(key => {
    templateForm[key] = row[key];
  });
  
  // 解析变量
  try {
    const variables = typeof row.variables === 'string' 
      ? JSON.parse(row.variables) 
      : row.variables || [];
    
    variablesList.value = Array.isArray(variables) 
      ? variables.map(v => typeof v === 'string' ? { name: v } : v) 
      : [];
  } catch (error) {
    console.error('解析变量失败:', error);
    variablesList.value = [];
  }
  
  dialogVisible.value = true;
};

// 预览模板
const handlePreview = (row) => {
  currentTemplate.value = row;
  renderedTemplate.value = null;
  
  // 初始化测试变量
  testVariables.value = {};
  try {
    const variables = typeof row.variables === 'string' 
      ? JSON.parse(row.variables) 
      : row.variables || [];
    
    variablesList.value = Array.isArray(variables) 
      ? variables.map(v => typeof v === 'string' ? { name: v } : v) 
      : [];
    
    // 设置默认值
    variablesList.value.forEach(v => {
      testVariables.value[v.name] = v.defaultValue || '';
    });
  } catch (error) {
    console.error('解析变量失败:', error);
    variablesList.value = [];
  }
  
  previewVisible.value = true;
};

// 删除模板
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '此操作将永久删除该模板, 是否继续?',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await axios.delete(`/api/message-templates/${row.id}`);
      
      if (response.data.code === 0) {
        ElMessage.success('删除成功');
        fetchTemplates();
      } else {
        ElMessage.error(response.data.message || '删除失败');
      }
    } catch (error) {
      console.error('删除模板失败:', error);
      ElMessage.error('删除模板失败: ' + (error.response?.data?.message || error.message));
    }
  }).catch(() => {
    // 取消删除
  });
};

// 修改状态
const handleStatusChange = async (row) => {
  try {
    const response = await axios.put(`/api/message-templates/${row.id}`, {
      isActive: row.isActive
    });
    
    if (response.data.code === 0) {
      ElMessage.success(`${row.isActive ? '启用' : '禁用'}成功`);
    } else {
      ElMessage.error(response.data.message || '操作失败');
      // 恢复原值
      row.isActive = !row.isActive;
    }
  } catch (error) {
    console.error('修改状态失败:', error);
    ElMessage.error('修改状态失败: ' + (error.response?.data?.message || error.message));
    // 恢复原值
    row.isActive = !row.isActive;
  }
};

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  if (templateFormRef.value) {
    templateFormRef.value.resetFields();
  }
  
  Object.keys(templateForm).forEach(key => {
    if (key === 'templateType') {
      templateForm[key] = 'card';
    } else if (key === 'isActive') {
      templateForm[key] = true;
    } else if (key === 'isDefault') {
      templateForm[key] = false;
    } else {
      templateForm[key] = '';
    }
  });
  
  variablesList.value = [];
};

// 提交表单
const submitForm = async () => {
  if (!templateFormRef.value) return;
  
  await templateFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 准备提交数据
        const submitData = { ...templateForm };
        
        // 转换变量列表为JSON
        submitData.variables = JSON.stringify(variablesList.value);
        
        // 发送请求
        let response;
        if (isEdit.value) {
          response = await axios.put(`/api/message-templates/${submitData.id}`, submitData);
        } else {
          response = await axios.post('/api/message-templates', submitData);
        }
        
        if (response.data.code === 0) {
          ElMessage.success(isEdit.value ? '更新成功' : '创建成功');
          dialogVisible.value = false;
          fetchTemplates();
        } else {
          ElMessage.error(response.data.message || (isEdit.value ? '更新失败' : '创建失败'));
        }
      } catch (error) {
        console.error(isEdit.value ? '更新模板失败:' : '创建模板失败:', error);
        ElMessage.error((isEdit.value ? '更新' : '创建') + '失败: ' + (error.response?.data?.message || error.message));
      }
    } else {
      return false;
    }
  });
};

// 测试渲染
const testRender = async () => {
  try {
    // 校验模板内容格式
    try {
      JSON.parse(templateForm.templateContent);
    } catch (error) {
      ElMessage.error('模板内容不是有效的JSON格式');
      return;
    }
    
    // 准备测试数据
    const variables = {};
    variablesList.value.forEach(v => {
      variables[v.name] = v.defaultValue || `测试${v.name}`;
    });
    
    const response = await axios.post('/api/message-templates/test-render', {
      templateData: templateForm,
      variables
    });
    
    if (response.data.code === 0) {
      renderedTemplate.value = response.data.data;
      previewVisible.value = true;
      currentTemplate.value = { ...templateForm };
      
      // 初始化测试变量
      testVariables.value = { ...variables };
    } else {
      ElMessage.error(response.data.message || '渲染失败');
    }
  } catch (error) {
    console.error('测试渲染失败:', error);
    ElMessage.error('测试渲染失败: ' + (error.response?.data?.message || error.message));
  }
};

// 在预览对话框中渲染
const handleRenderPreview = async () => {
  try {
    const templateData = {
      templateName: currentTemplate.value.templateName,
      templateType: currentTemplate.value.templateType,
      templateContent: currentTemplate.value.templateContent
    };
    
    const response = await axios.post('/api/message-templates/test-render', {
      templateData,
      variables: testVariables.value
    });
    
    if (response.data.code === 0) {
      renderedTemplate.value = response.data.data;
    } else {
      ElMessage.error(response.data.message || '渲染失败');
    }
  } catch (error) {
    console.error('渲染预览失败:', error);
    ElMessage.error('渲染预览失败: ' + (error.response?.data?.message || error.message));
  }
};

// 变量处理
const showVariableInput = () => {
  variableInputVisible.value = true;
  nextTick(() => {
    variableInputRef.value.focus();
  });
};

const handleAddVariable = () => {
  if (variableInput.value) {
    variablesList.value.push({ name: variableInput.value });
    variableInput.value = '';
  }
  variableInputVisible.value = false;
};

const handleRemoveVariable = (index) => {
  variablesList.value.splice(index, 1);
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString();
};

// 获取通知类型标签样式
const getNotificationTypeTag = (type) => {
  const typeMap = {
    exchange: 'success',
    system: 'danger',
    user: 'primary',
    feedback: 'warning',
    report: 'info'
  };
  return typeMap[type] || '';
};

// 获取通知类型名称
const getNotificationTypeName = (type) => {
  const typeMap = {
    exchange: '订单消息',
    system: '系统通知',
    user: '用户消息',
    feedback: '反馈回复',
    report: '日报周报'
  };
  return typeMap[type] || type;
};

// 获取模板类型标签样式
const getTemplateTypeTag = (type) => {
  const typeMap = {
    text: '',
    card: 'success',
    rich: 'warning'
  };
  return typeMap[type] || '';
};

// 获取模板类型名称
const getTemplateTypeName = (type) => {
  const typeMap = {
    text: '纯文本',
    card: '卡片消息',
    rich: '富文本'
  };
  return typeMap[type] || type;
};
</script>

<style scoped>
.message-templates-container {
  padding: 16px;
}

.message-templates-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.filter-container {
  margin-bottom: 16px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.variable-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.variable-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.variable-input {
  width: 120px;
  margin-right: 8px;
  vertical-align: top;
}

.hint {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.preview-container {
  padding: 16px;
}

.preview-header {
  margin-bottom: 16px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.preview-type {
  font-size: 14px;
  color: #909399;
}

.preview-content {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  min-height: 200px;
  max-height: 400px;
  overflow: auto;
}

.preview-content pre {
  white-space: pre-wrap;
  word-break: break-all;
}

.preview-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
}

.preview-variables {
  border-top: 1px solid #eee;
  padding-top: 16px;
}
</style> 