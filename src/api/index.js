import axios from 'axios';
import { useAuthStore } from '../stores/auth';

// 创建一个axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000/api', // 从环境变量获取API URL
  timeout: 15000, // 增加请求超时时间为15秒
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    const authStore = useAuthStore();
    
    // 特殊处理登录请求
    if (config.url === '/auth/login') {
      console.log('准备发送登录请求', JSON.stringify(config.data));
    }
    
    // 需要认证的API路径列表
    const requireAuthPaths = [
      '/notifications',
      '/auth/profile',
      '/auth/logout',
      '/users',
      '/admin',
      '/products/upload',
      '/feedback'
    ];
    
    // 检查是否需要认证
    const needsAuth = requireAuthPaths.some(path => config.url.startsWith(path));
    
    if (authStore.token) {
      config.headers['Authorization'] = `Bearer ${authStore.token}`;
      console.log(`API请求: ${config.method.toUpperCase()} ${config.url} (已授权)`);
    } else if (needsAuth) {
      console.warn(`API请求被阻止: ${config.method.toUpperCase()} ${config.url} (需要认证但无token)`);
      return Promise.reject({
        message: '用户未登录，请先登录',
        status: 401,
        config
      });
    } else {
      console.log(`API请求: ${config.method.toUpperCase()} ${config.url} (公开接口)`);
    }
    
    // 对于文件上传请求，确保不设置Content-Type
    if (config.data instanceof FormData) {
      console.log('检测到FormData，自动处理文件上传设置');
      delete config.headers['Content-Type'];
    }
    
    return config;
  },
  error => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log(`API响应成功: ${response.config.method.toUpperCase()} ${response.config.url}`);
    console.log('响应数据:', JSON.stringify(response.data).substring(0, 200) + '...');
    // 返回响应数据
    return response.data;
  },
  error => {
    console.error('API请求错误:', error);
    
    // 网络错误
    if (!error.response) {
      console.error('网络错误，无法连接到服务器');
      return Promise.reject({
        message: '网络错误，无法连接到服务器',
        status: 0
      });
    }
    
    console.error(`API响应错误: ${error.response.status} ${error.response.statusText}`);
    console.error('错误响应数据:', error.response.data);
    console.error('请求URL:', error.config.url);
    console.error('请求方法:', error.config.method);
    console.error('请求数据:', error.config.data);
    
    // 处理401错误 (未授权)
    if (error.response && error.response.status === 401) {
      const authStore = useAuthStore();
      // 如果token存在但已过期，则登出
      if (authStore.token) {
        console.warn('Token已过期，正在登出...');
        authStore.logout();
      }
    }
    
    return Promise.reject(error.response?.data || {
      message: error.message || '请求失败',
      status: error.response?.status || 500
    });
  }
);

export default api; 