const messageTemplateService = require('./services/messageTemplateService');
const MessageTemplate = require('./models/messageTemplate');

async function testTemplatePreview() {
  try {
    console.log('=== 开始测试模板预览功能 ===');
    
    // 1. 测试数据库连接
    console.log('1. 测试数据库连接...');
    const { sequelize } = require('./config/database');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 2. 检查MessageTemplate模型
    console.log('2. 检查MessageTemplate模型...');
    console.log('MessageTemplate模型:', MessageTemplate.name);
    console.log('表名:', MessageTemplate.tableName);
    
    // 3. 检查表数据
    console.log('3. 检查表数据...');
    const count = await MessageTemplate.count();
    console.log('表中记录数:', count);
    
    // 4. 添加测试模板数据
    console.log('4. 添加测试模板数据...');
    const testTemplate = {
      templateName: '测试模板',
      templateCode: 'test_template',
      notificationType: 'exchange_notification',
      templateType: 'card',
      templateContent: JSON.stringify({
        msg_type: 'interactive',
        card: {
          header: {
            title: {
              content: '兑换通知 - {{title}}',
              tag: 'text'
            }
          },
          elements: [
            {
              tag: 'div',
              text: {
                content: '用户 {{userName}} 申请兑换商品：{{productName}}',
                tag: 'lark_md'
              }
            },
            {
              tag: 'div',
              text: {
                content: '兑换时间：{{exchangeTime}}',
                tag: 'lark_md'
              }
            }
          ]
        }
      }),
      variables: JSON.stringify([
        { name: 'title', defaultValue: '商品兑换' },
        { name: 'userName', defaultValue: '测试用户' },
        { name: 'productName', defaultValue: '测试商品' },
        { name: 'exchangeTime', defaultValue: '2025-06-04 14:30:00' }
      ]),
      description: '测试模板描述',
      isActive: true,
      createdBy: 1
    };
    
    // 先删除可能存在的测试数据
    await MessageTemplate.destroy({
      where: { templateCode: 'test_template' }
    });
    
    const template = await MessageTemplate.create(testTemplate);
    console.log('✅ 测试模板创建成功，ID:', template.id);
    
    // 5. 测试testRenderTemplate方法
    console.log('5. 测试testRenderTemplate方法...');
    const templateData = {
      templateName: '测试模板',
      templateType: 'card',
      templateContent: testTemplate.templateContent
    };
    
    const variables = {
      title: '紧急兑换',
      userName: '张三',
      productName: 'iPhone 15',
      exchangeTime: '2025-06-04 15:00:00'
    };
    
    const renderedResult = messageTemplateService.testRenderTemplate(templateData, variables);
    console.log('✅ 模板渲染成功');
    console.log('渲染结果:', JSON.stringify(renderedResult, null, 2));
    
    // 6. 测试不同类型的模板
    console.log('6. 测试文本类型模板...');
    const textTemplate = {
      templateName: '文本模板',
      templateType: 'text',
      templateContent: JSON.stringify({
        msg_type: 'text',
        content: {
          text: '用户{{userName}}申请兑换{{productName}}，请及时处理。'
        }
      })
    };
    
    const textResult = messageTemplateService.testRenderTemplate(textTemplate, variables);
    console.log('✅ 文本模板渲染成功');
    console.log('文本渲染结果:', JSON.stringify(textResult, null, 2));
    
    console.log('✅ 所有测试通过');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误堆栈:', error.stack);
  } finally {
    process.exit(0);
  }
}

testTemplatePreview();
