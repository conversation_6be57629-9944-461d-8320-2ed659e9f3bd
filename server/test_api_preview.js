const axios = require('axios');

async function testAPIPreview() {
  try {
    console.log('=== 测试模板预览API ===');
    
    const testData = {
      templateData: {
        templateName: "测试模板",
        templateType: "card",
        templateContent: JSON.stringify({
          msg_type: "interactive",
          card: {
            header: {
              title: {
                content: "兑换通知 - {{title}}",
                tag: "text"
              }
            },
            elements: [
              {
                tag: "div",
                text: {
                  content: "用户 {{userName}} 申请兑换商品：{{productName}}",
                  tag: "lark_md"
                }
              },
              {
                tag: "div",
                text: {
                  content: "兑换时间：{{exchangeTime}}",
                  tag: "lark_md"
                }
              }
            ]
          }
        })
      },
      variables: {
        title: "紧急兑换",
        userName: "张三",
        productName: "iPhone 15",
        exchangeTime: "2025-06-04 15:00:00"
      }
    };
    
    console.log('发送请求到: http://localhost:3001/api/message-templates/test-render');
    console.log('请求数据:', JSON.stringify(testData, null, 2));
    
    const response = await axios.post('http://localhost:3001/api/message-templates/test-render', testData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ API调用成功');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ API调用失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    console.error('错误堆栈:', error.stack);
  }
}

testAPIPreview();
