const messageTemplateService = require('../services/messageTemplateService');

/**
 * 消息模板控制器
 */
class MessageTemplateController {
  /**
   * 获取模板列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getTemplates(req, res) {
    try {
      const options = {
        page: parseInt(req.query.page) || 1,
        pageSize: parseInt(req.query.pageSize) || 20,
        notificationType: req.query.notificationType,
        isActive: req.query.isActive !== undefined ? req.query.isActive === 'true' : undefined,
        search: req.query.search,
        sortBy: req.query.sortBy || 'id',
        sortOrder: req.query.sortOrder || 'DESC'
      };
      
      const templates = await messageTemplateService.getTemplates(options);
      res.json({
        code: 0,
        data: templates,
        message: '获取模板列表成功'
      });
    } catch (error) {
      console.error('获取模板列表失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取模板列表失败: ' + error.message
      });
    }
  }

  /**
   * 获取模板详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getTemplateById(req, res) {
    try {
      const id = req.params.id;
      const template = await messageTemplateService.getTemplateById(id);
      
      res.json({
        code: 0,
        data: template,
        message: '获取模板详情成功'
      });
    } catch (error) {
      console.error('获取模板详情失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取模板详情失败: ' + error.message
      });
    }
  }

  /**
   * 创建模板
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createTemplate(req, res) {
    try {
      const templateData = req.body;
      const template = await messageTemplateService.createTemplate(templateData);
      
      res.json({
        code: 0,
        data: template,
        message: '创建模板成功'
      });
    } catch (error) {
      console.error('创建模板失败:', error);
      res.status(500).json({
        code: 500,
        message: '创建模板失败: ' + error.message
      });
    }
  }

  /**
   * 更新模板
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateTemplate(req, res) {
    try {
      const id = req.params.id;
      const templateData = req.body;
      const template = await messageTemplateService.updateTemplate(id, templateData);
      
      res.json({
        code: 0,
        data: template,
        message: '更新模板成功'
      });
    } catch (error) {
      console.error('更新模板失败:', error);
      res.status(500).json({
        code: 500,
        message: '更新模板失败: ' + error.message
      });
    }
  }

  /**
   * 删除模板
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async deleteTemplate(req, res) {
    try {
      const id = req.params.id;
      await messageTemplateService.deleteTemplate(id);
      
      res.json({
        code: 0,
        message: '删除模板成功'
      });
    } catch (error) {
      console.error('删除模板失败:', error);
      res.status(500).json({
        code: 500,
        message: '删除模板失败: ' + error.message
      });
    }
  }

  /**
   * 测试渲染模板
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async testRenderTemplate(req, res) {
    try {
      const { templateData, variables } = req.body;
      
      if (!templateData || !templateData.templateContent) {
        return res.status(400).json({
          code: 400,
          message: '模板数据不完整'
        });
      }
      
      const renderedMessage = messageTemplateService.testRenderTemplate(templateData, variables || {});
      
      res.json({
        code: 0,
        data: renderedMessage,
        message: '测试渲染成功'
      });
    } catch (error) {
      console.error('测试渲染失败:', error);
      res.status(500).json({
        code: 500,
        message: '测试渲染失败: ' + error.message
      });
    }
  }
}

module.exports = new MessageTemplateController(); 