const MessageTemplate = require('../models/messageTemplate');
const Handlebars = require('handlebars');
const { Op } = require('sequelize');

/**
 * 消息模板服务
 * 用于管理和渲染消息模板
 */
class MessageTemplateService {
  /**
   * 获取模板列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} - 模板列表
   */
  async getTemplates(options = {}) {
    const {
      page = 1,
      pageSize = 20,
      notificationType,
      isActive,
      search,
      sortBy = 'id',
      sortOrder = 'DESC'
    } = options;

    const where = {};
    
    if (notificationType) {
      where.notificationType = notificationType;
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive;
    }
    
    if (search) {
      where[Op.or] = [
        { templateName: { [Op.like]: `%${search}%` } },
        { templateCode: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows } = await MessageTemplate.findAndCountAll({
      where,
      order: [[sortBy, sortOrder]],
      limit: pageSize,
      offset: (page - 1) * pageSize
    });

    return {
      total: count,
      page,
      pageSize,
      data: rows
    };
  }

  /**
   * 根据ID获取模板
   * @param {number} id - 模板ID
   * @returns {Promise<Object>} - 模板对象
   */
  async getTemplateById(id) {
    const template = await MessageTemplate.findByPk(id);
    if (!template) {
      throw new Error('模板不存在');
    }
    return template;
  }

  /**
   * 根据模板代码获取模板
   * @param {string} code - 模板代码
   * @returns {Promise<Object>} - 模板对象
   */
  async getTemplateByCode(code) {
    const template = await MessageTemplate.findOne({
      where: { templateCode: code }
    });
    
    if (!template) {
      throw new Error(`模板代码 ${code} 不存在`);
    }
    
    return template;
  }

  /**
   * 根据通知类型获取默认模板
   * @param {string} notificationType - 通知类型
   * @returns {Promise<Object>} - 模板对象
   */
  async getDefaultTemplateByType(notificationType) {
    const template = await MessageTemplate.findOne({
      where: {
        notificationType,
        isDefault: true,
        isActive: true
      }
    });
    
    if (!template) {
      throw new Error(`类型 ${notificationType} 的默认模板不存在`);
    }
    
    return template;
  }

  /**
   * 创建新模板
   * @param {Object} templateData - 模板数据
   * @returns {Promise<Object>} - 创建的模板对象
   */
  async createTemplate(templateData) {
    // 检查模板代码唯一性
    if (templateData.templateCode) {
      const existingTemplate = await MessageTemplate.findOne({
        where: { templateCode: templateData.templateCode }
      });
      
      if (existingTemplate) {
        throw new Error(`模板代码 ${templateData.templateCode} 已存在`);
      }
    }
    
    // 检查默认模板
    if (templateData.isDefault) {
      await this._handleDefaultTemplate(templateData.notificationType);
    }
    
    return await MessageTemplate.create(templateData);
  }

  /**
   * 更新模板
   * @param {number} id - 模板ID
   * @param {Object} templateData - 更新的模板数据
   * @returns {Promise<Object>} - 更新后的模板对象
   */
  async updateTemplate(id, templateData) {
    const template = await this.getTemplateById(id);
    
    // 检查模板代码唯一性
    if (templateData.templateCode && templateData.templateCode !== template.templateCode) {
      const existingTemplate = await MessageTemplate.findOne({
        where: { 
          templateCode: templateData.templateCode,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingTemplate) {
        throw new Error(`模板代码 ${templateData.templateCode} 已存在`);
      }
    }
    
    // 检查默认模板
    if (templateData.isDefault && !template.isDefault) {
      await this._handleDefaultTemplate(templateData.notificationType || template.notificationType);
    }
    
    await template.update(templateData);
    return template;
  }

  /**
   * 删除模板
   * @param {number} id - 模板ID
   * @returns {Promise<boolean>} - 是否删除成功
   */
  async deleteTemplate(id) {
    const template = await this.getTemplateById(id);
    
    // 不允许删除默认模板
    if (template.isDefault) {
      throw new Error('不能删除默认模板，请先设置其他模板为默认');
    }
    
    // 检查模板是否正在使用中
    if (template.usageCount > 0) {
      throw new Error('模板正在使用中，不能删除');
    }
    
    await template.destroy();
    return true;
  }

  /**
   * 渲染模板
   * @param {string} templateCode - 模板代码
   * @param {Object} variables - 模板变量
   * @returns {Promise<Object>} - 渲染后的消息对象
   */
  async renderTemplate(templateCode, variables = {}) {
    try {
      const template = await this.getTemplateByCode(templateCode);
      const templateContent = template.templateContent;
      
      // 解析JSON
      let messageObject;
      try {
        if (typeof templateContent === 'string') {
          messageObject = JSON.parse(templateContent);
        } else {
          messageObject = templateContent;
        }
      } catch (error) {
        throw new Error(`模板内容不是有效的JSON: ${error.message}`);
      }
      
      // 使用Handlebars渲染模板
      const renderedMessage = this._renderObject(messageObject, variables);
      
      // 更新使用计数
      await template.increment('usageCount');
      
      return renderedMessage;
    } catch (error) {
      console.error('渲染模板失败:', error);
      throw error;
    }
  }

  /**
   * 测试渲染模板
   * @param {Object} templateData - 模板数据
   * @param {Object} variables - 测试变量
   * @returns {Object} - 渲染后的消息对象
   */
  testRenderTemplate(templateData, variables = {}) {
    try {
      console.log('开始测试渲染模板:', templateData?.templateName || '未知模板');

      // 解析JSON
      let messageObject;
      try {
        if (typeof templateData.templateContent === 'string') {
          console.log('解析字符串格式的模板内容');
          messageObject = JSON.parse(templateData.templateContent);
        } else {
          console.log('使用对象格式的模板内容');
          messageObject = templateData.templateContent;
        }
        console.log('JSON解析成功');
      } catch (error) {
        console.error('JSON解析失败:', error);
        throw new Error(`模板内容不是有效的JSON: ${error.message}`);
      }

      // 使用Handlebars渲染模板
      console.log('开始使用Handlebars渲染');
      const result = this._renderObject(messageObject, variables);
      console.log('渲染完成');
      return result;
    } catch (error) {
      console.error('测试渲染模板失败:', error);
      console.error('错误堆栈:', error.stack);
      throw error;
    }
  }

  /**
   * 递归渲染对象中的所有字符串值
   * @param {*} obj - 需要渲染的对象或值
   * @param {Object} variables - 模板变量
   * @returns {*} - 渲染后的对象或值
   * @private
   */
  _renderObject(obj, variables) {
    if (typeof obj === 'string') {
      try {
        const template = Handlebars.compile(obj);
        return template(variables);
      } catch (error) {
        console.warn(`渲染字符串失败: ${obj}`, error);
        return obj;
      }
    } else if (Array.isArray(obj)) {
      return obj.map(item => this._renderObject(item, variables));
    } else if (obj !== null && typeof obj === 'object') {
      const result = {};
      for (const [key, value] of Object.entries(obj)) {
        result[key] = this._renderObject(value, variables);
      }
      return result;
    }
    return obj;
  }

  /**
   * 处理默认模板（确保每种类型只有一个默认模板）
   * @param {string} notificationType - 通知类型
   * @private
   */
  async _handleDefaultTemplate(notificationType) {
    await MessageTemplate.update(
      { isDefault: false },
      { 
        where: { 
          notificationType,
          isDefault: true
        }
      }
    );
  }
}

module.exports = new MessageTemplateService(); 